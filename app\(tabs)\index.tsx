import { Card } from '@/components/ui/Card';
import { Colors, Currencies, Spacing, Typography } from '@/constants/Theme';
import { useAuth } from '@/context/AuthContext';
import { getDashboardData, getUpcomingEMIs } from '@/utils/database';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface DashboardData {
  monthlyExpenses: number;
  totalInvestments: number;
  upcomingEMIs: number;
}

export default function DashboardScreen() {
  const { user, logout } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    monthlyExpenses: 0,
    totalInvestments: 0,
    upcomingEMIs: 0,
  });
  const [upcomingEMIs, setUpcomingEMIs] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [expenseChartData, setExpenseChartData] = useState<any>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'year'>('month');

  const loadDashboardData = useCallback(async () => {
    if (!user?.id) return;

    try {
      const data = getDashboardData(user.id);
      setDashboardData(data);

      const emis = getUpcomingEMIs(user.id, 7);
      setUpcomingEMIs(emis);

      // Load expense data for chart
      const expenses = getExpenses(user.id);
      const chartData = formatExpenseChartData(expenses, selectedTimeframe);
      setExpenseChartData(chartData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }, [user?.id]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    const currency = user?.preferred_currency || 'NRP';
    const symbol = Currencies[currency as keyof typeof Currencies]?.symbol || '₨';
    return `${symbol}${amount.toLocaleString()}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Good morning,</Text>
            <Text style={styles.username}>{user?.username}</Text>
          </View>
          <TouchableOpacity onPress={logout} style={styles.logoutButton}>
            <Ionicons name="log-out-outline" size={24} color={Colors.text.secondary} />
          </TouchableOpacity>
        </View>

        {/* Financial Overview Cards */}
        <View style={styles.overviewSection}>
          <Text style={styles.sectionTitle}>Financial Overview</Text>

          <View style={styles.cardRow}>
            <Card style={[styles.overviewCard, { backgroundColor: Colors.primary[50] }] as any}>
              <View style={styles.cardContent}>
                <Ionicons name="receipt-outline" size={24} color={Colors.primary[600]} />
                <Text style={styles.cardValue}>{formatCurrency(dashboardData.monthlyExpenses)}</Text>
                <Text style={styles.cardLabel}>Monthly Expenses</Text>
              </View>
            </Card>

            <Card style={[styles.overviewCard, { backgroundColor: Colors.success + '10' }] as any}>
              <View style={styles.cardContent}>
                <Ionicons name="trending-up-outline" size={24} color={Colors.success} />
                <Text style={styles.cardValue}>{formatCurrency(dashboardData.totalInvestments)}</Text>
                <Text style={styles.cardLabel}>Total Investments</Text>
              </View>
            </Card>
          </View>
        </View>

        {/* Upcoming EMIs */}
        <View style={styles.emisSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming EMIs</Text>
            <Text style={styles.sectionSubtitle}>Next 7 days</Text>
          </View>

          {upcomingEMIs.length > 0 ? (
            upcomingEMIs.slice(0, 3).map((emi, index) => (
              <Card key={index} style={styles.emiCard}>
                <View style={styles.emiContent}>
                  <View style={styles.emiInfo}>
                    <Text style={styles.emiLoanName}>{emi.loan_name}</Text>
                    <Text style={styles.emiAmount}>{formatCurrency(emi.amount)}</Text>
                  </View>
                  <View style={styles.emiDate}>
                    <Text style={styles.emiDueDate}>{new Date(emi.due_date).toLocaleDateString()}</Text>
                    <View style={[styles.statusBadge, { backgroundColor: Colors.warning + '20' }]}>
                      <Text style={[styles.statusText, { color: Colors.warning }]}>Due Soon</Text>
                    </View>
                  </View>
                </View>
              </Card>
            ))
          ) : (
            <Card style={styles.emptyCard}>
              <Text style={styles.emptyText}>No upcoming EMIs</Text>
            </Card>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
  },
  greeting: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  username: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
  },
  logoutButton: {
    padding: Spacing.sm,
  },
  overviewSection: {
    paddingHorizontal: Spacing['2xl'],
    marginBottom: Spacing['2xl'],
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  overviewCard: {
    flex: 1,
    padding: Spacing.lg,
  },
  cardContent: {
    alignItems: 'center',
  },
  cardValue: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
    marginTop: Spacing.sm,
  },
  cardLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  emisSection: {
    paddingHorizontal: Spacing['2xl'],
    marginBottom: Spacing['2xl'],
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  emiCard: {
    marginBottom: Spacing.md,
  },
  emiContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emiInfo: {
    flex: 1,
  },
  emiLoanName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
  },
  emiAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.primary[600],
    marginTop: Spacing.xs,
  },
  emiDate: {
    alignItems: 'flex-end',
  },
  emiDueDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium as any,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: Spacing['2xl'],
  },
  emptyText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
});
