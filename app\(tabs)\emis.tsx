import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Colors, Currencies, Spacing, Typography } from '@/constants/Theme';
import { useAuth } from '@/context/AuthContext';
import { useNotifications } from '@/context/NotificationContext';
import {
  addLoan,
  deleteLoan,
  EMI,
  getEMIs,
  getLoans,
  getLoanSummary,
  Loan,
  markEMIAsPaid,
  updateLoan
} from '@/utils/database';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface LoanFormData {
  name: string;
  principalAmount: string;
  interestRate: string;
  startDate: string;
  tenureMonths: string;
}

interface LoanWithEMIs extends Loan {
  emis: EMI[];
  paidEMIs: number;
  unpaidEMIs: number;
}

export default function EMIsScreen() {
  const { user } = useAuth();
  const { rescheduleAllNotifications, cancelEMINotifications } = useNotifications();
  const [loans, setLoans] = useState<LoanWithEMIs[]>([]);
  const [summary, setSummary] = useState<any>({
    total_loans: 0,
    total_principal: 0,
    pending_emis: 0,
  });
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingLoan, setEditingLoan] = useState<Loan | null>(null);
  const [selectedLoan, setSelectedLoan] = useState<LoanWithEMIs | null>(null);
  const [emiModalVisible, setEmiModalVisible] = useState(false);
  const [formData, setFormData] = useState<LoanFormData>({
    name: '',
    principalAmount: '',
    interestRate: '',
    startDate: new Date().toISOString().split('T')[0],
    tenureMonths: '',
  });

  const loadLoans = useCallback(async () => {
    if (!user?.id) return;

    try {
      const loanList = getLoans(user.id);
      const loansWithEMIs: LoanWithEMIs[] = loanList.map(loan => {
        const emis = getEMIs(loan.id!);
        const paidEMIs = emis.filter(emi => emi.status === 'paid').length;
        const unpaidEMIs = emis.filter(emi => emi.status === 'unpaid').length;

        return {
          ...loan,
          emis,
          paidEMIs,
          unpaidEMIs,
        };
      });

      setLoans(loansWithEMIs);

      const summaryData = getLoanSummary(user.id);
      setSummary(summaryData || { total_loans: 0, total_principal: 0, pending_emis: 0 });
    } catch (error) {
      console.error('Error loading loans:', error);
      Alert.alert('Error', 'Failed to load loans');
    }
  }, [user?.id]);

  useEffect(() => {
    loadLoans();
  }, [loadLoans]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLoans();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    const currency = user?.preferred_currency || 'NRP';
    const symbol = Currencies[currency as keyof typeof Currencies]?.symbol || '₨';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const calculateEMIAmount = (principal: number, rate: number, tenure: number) => {
    const monthlyRate = rate / 100 / 12;
    const emiAmount = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenure)) /
                      (Math.pow(1 + monthlyRate, tenure) - 1);
    return Math.round(emiAmount * 100) / 100;
  };

  const handleAddLoan = () => {
    setEditingLoan(null);
    setFormData({
      name: '',
      principalAmount: '',
      interestRate: '',
      startDate: new Date().toISOString().split('T')[0],
      tenureMonths: '',
    });
    setModalVisible(true);
  };

  const handleEditLoan = (loan: Loan) => {
    setEditingLoan(loan);
    setFormData({
      name: loan.name,
      principalAmount: loan.principal_amount.toString(),
      interestRate: loan.interest_rate.toString(),
      startDate: loan.start_date,
      tenureMonths: loan.tenure_months.toString(),
    });
    setModalVisible(true);
  };

  const handleDeleteLoan = (loan: Loan) => {
    Alert.alert(
      'Delete Loan',
      'Are you sure you want to delete this loan? This will also delete all associated EMIs.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            try {
              deleteLoan(loan.id!);
              loadLoans();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete loan');
            }
          },
        },
      ]
    );
  };

  const handleSaveLoan = async () => {
    if (!user?.id || !formData.name || !formData.principalAmount || !formData.interestRate || !formData.tenureMonths) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const principalAmount = parseFloat(formData.principalAmount);
    const interestRate = parseFloat(formData.interestRate);
    const tenureMonths = parseInt(formData.tenureMonths);

    if (isNaN(principalAmount) || principalAmount <= 0) {
      Alert.alert('Error', 'Please enter a valid principal amount');
      return;
    }

    if (isNaN(interestRate) || interestRate < 0) {
      Alert.alert('Error', 'Please enter a valid interest rate');
      return;
    }

    if (isNaN(tenureMonths) || tenureMonths <= 0) {
      Alert.alert('Error', 'Please enter a valid tenure in months');
      return;
    }

    try {
      if (editingLoan) {
        updateLoan(
          editingLoan.id!,
          formData.name,
          principalAmount,
          interestRate,
          formData.startDate,
          tenureMonths
        );
      } else {
        addLoan(
          user.id,
          formData.name,
          principalAmount,
          interestRate,
          formData.startDate,
          tenureMonths
        );
      }

      setModalVisible(false);
      loadLoans();

      // Reschedule notifications for all EMIs
      await rescheduleAllNotifications();
    } catch (error) {
      Alert.alert('Error', 'Failed to save loan');
    }
  };

  const handleViewEMIs = (loan: LoanWithEMIs) => {
    setSelectedLoan(loan);
    setEmiModalVisible(true);
  };

  const handleMarkEMIPaid = (emi: EMI) => {
    Alert.alert(
      'Mark EMI as Paid',
      `Mark EMI of ${formatCurrency(emi.amount)} as paid?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark Paid',
          onPress: async () => {
            try {
              markEMIAsPaid(emi.id!, new Date().toISOString().split('T')[0]);

              // Cancel notifications for this EMI
              await cancelEMINotifications(emi.id!);

              loadLoans();
              // Update selected loan EMIs
              if (selectedLoan) {
                const updatedEMIs = getEMIs(selectedLoan.id!);
                setSelectedLoan({
                  ...selectedLoan,
                  emis: updatedEMIs,
                  paidEMIs: updatedEMIs.filter(e => e.status === 'paid').length,
                  unpaidEMIs: updatedEMIs.filter(e => e.status === 'unpaid').length,
                });
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to mark EMI as paid');
            }
          },
        },
      ]
    );
  };

  const getEMIStatusColor = (emi: EMI) => {
    if (emi.status === 'paid') return Colors.success;

    const dueDate = new Date(emi.due_date);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return Colors.error; // Overdue
    if (diffDays <= 3) return Colors.warning; // Due soon
    return Colors.text.secondary; // Normal
  };

  const getEMIStatusText = (emi: EMI) => {
    if (emi.status === 'paid') return 'Paid';

    const dueDate = new Date(emi.due_date);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due Today';
    if (diffDays <= 3) return 'Due Soon';
    return 'Upcoming';
  };

  const renderLoanItem = ({ item }: { item: LoanWithEMIs }) => {
    const emiAmount = calculateEMIAmount(item.principal_amount, item.interest_rate, item.tenure_months);
    const progress = (item.paidEMIs / item.tenure_months) * 100;

    return (
      <Card style={styles.loanCard}>
        <View style={styles.loanContent}>
          <View style={styles.loanHeader}>
            <Text style={styles.loanName}>{item.name}</Text>
            <Text style={styles.loanAmount}>{formatCurrency(item.principal_amount)}</Text>
          </View>

          <View style={styles.loanDetails}>
            <View style={styles.loanDetailItem}>
              <Text style={styles.loanDetailLabel}>EMI Amount</Text>
              <Text style={styles.loanDetailValue}>{formatCurrency(emiAmount)}</Text>
            </View>
            <View style={styles.loanDetailItem}>
              <Text style={styles.loanDetailLabel}>Interest Rate</Text>
              <Text style={styles.loanDetailValue}>{item.interest_rate}%</Text>
            </View>
            <View style={styles.loanDetailItem}>
              <Text style={styles.loanDetailLabel}>Progress</Text>
              <Text style={styles.loanDetailValue}>{item.paidEMIs}/{item.tenure_months}</Text>
            </View>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.progressText}>{progress.toFixed(1)}%</Text>
          </View>

          <View style={styles.loanActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleViewEMIs(item)}
            >
              <Ionicons name="list" size={16} color={Colors.primary[600]} />
              <Text style={styles.actionButtonText}>View EMIs</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditLoan(item)}
            >
              <Ionicons name="pencil" size={16} color={Colors.primary[600]} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteLoan(item)}
            >
              <Ionicons name="trash" size={16} color={Colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  const renderEMIItem = ({ item }: { item: EMI }) => (
    <Card style={styles.emiCard}>
      <View style={styles.emiContent}>
        <View style={styles.emiInfo}>
          <Text style={styles.emiAmount}>{formatCurrency(item.amount)}</Text>
          <Text style={styles.emiDueDate}>Due: {new Date(item.due_date).toLocaleDateString()}</Text>
          {item.payment_date && (
            <Text style={styles.emiPaymentDate}>
              Paid: {new Date(item.payment_date).toLocaleDateString()}
            </Text>
          )}
        </View>
        <View style={styles.emiStatus}>
          <View style={[styles.statusBadge, { backgroundColor: getEMIStatusColor(item) + '20' }]}>
            <Text style={[styles.statusText, { color: getEMIStatusColor(item) }]}>
              {getEMIStatusText(item)}
            </Text>
          </View>
          {item.status === 'unpaid' && (
            <TouchableOpacity
              style={styles.payButton}
              onPress={() => handleMarkEMIPaid(item)}
            >
              <Text style={styles.payButtonText}>Mark Paid</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Loans & EMIs</Text>
          <TouchableOpacity style={styles.addButton} onPress={handleAddLoan}>
            <Ionicons name="add" size={24} color={Colors.text.inverse} />
          </TouchableOpacity>
        </View>

        {/* Summary */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Loan Summary</Text>

          <View style={styles.summaryCards}>
            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="card-outline" size={24} color={Colors.primary[600]} />
                <Text style={styles.summaryValue}>{summary.total_loans}</Text>
                <Text style={styles.summaryLabel}>Active Loans</Text>
              </View>
            </Card>

            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="wallet-outline" size={24} color={Colors.warning} />
                <Text style={styles.summaryValue}>{formatCurrency(summary.total_principal)}</Text>
                <Text style={styles.summaryLabel}>Total Principal</Text>
              </View>
            </Card>

            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="time-outline" size={24} color={Colors.error} />
                <Text style={styles.summaryValue}>{summary.pending_emis}</Text>
                <Text style={styles.summaryLabel}>Pending EMIs</Text>
              </View>
            </Card>
          </View>
        </View>

        {/* Loans List */}
        <View style={styles.loansSection}>
          <Text style={styles.sectionTitle}>Your Loans</Text>

          {loans.length > 0 ? (
            <FlatList
              data={loans}
              renderItem={renderLoanItem}
              keyExtractor={(item) => item.id!.toString()}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <Card style={styles.emptyCard}>
              <Ionicons name="card-outline" size={48} color={Colors.text.tertiary} />
              <Text style={styles.emptyText}>No loans yet</Text>
              <Text style={styles.emptySubtext}>Tap the + button to add your first loan</Text>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Add/Edit Loan Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {editingLoan ? 'Edit Loan' : 'Add Loan'}
            </Text>
            <TouchableOpacity onPress={handleSaveLoan}>
              <Text style={styles.saveButton}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Input
              label="Loan Name *"
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder="e.g., Home Loan, Car Loan"
            />

            <Input
              label="Principal Amount *"
              value={formData.principalAmount}
              onChangeText={(text) => setFormData({ ...formData, principalAmount: text })}
              placeholder="0.00"
              keyboardType="numeric"
            />

            <Input
              label="Interest Rate (% per annum) *"
              value={formData.interestRate}
              onChangeText={(text) => setFormData({ ...formData, interestRate: text })}
              placeholder="0.00"
              keyboardType="numeric"
            />

            <Input
              label="Tenure (Months) *"
              value={formData.tenureMonths}
              onChangeText={(text) => setFormData({ ...formData, tenureMonths: text })}
              placeholder="12"
              keyboardType="numeric"
            />

            <Input
              label="Start Date *"
              value={formData.startDate}
              onChangeText={(text) => setFormData({ ...formData, startDate: text })}
              placeholder="YYYY-MM-DD"
            />

            {formData.principalAmount && formData.interestRate && formData.tenureMonths && (
              <Card style={styles.emiPreview}>
                <Text style={styles.emiPreviewLabel}>Estimated EMI Amount:</Text>
                <Text style={styles.emiPreviewAmount}>
                  {formatCurrency(
                    calculateEMIAmount(
                      parseFloat(formData.principalAmount) || 0,
                      parseFloat(formData.interestRate) || 0,
                      parseInt(formData.tenureMonths) || 1
                    )
                  )}
                </Text>
              </Card>
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* EMI List Modal */}
      <Modal
        visible={emiModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEmiModalVisible(false)}>
              <Text style={styles.cancelButton}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {selectedLoan?.name} - EMIs
            </Text>
            <View style={{ width: 50 }} />
          </View>

          <View style={styles.modalContent}>
            {selectedLoan && (
              <FlatList
                data={selectedLoan.emis}
                renderItem={renderEMIItem}
                keyExtractor={(item) => item.id!.toString()}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={
                  <Card style={styles.emiSummary}>
                    <Text style={styles.emiSummaryTitle}>EMI Summary</Text>
                    <View style={styles.emiSummaryRow}>
                      <Text style={styles.emiSummaryLabel}>Total EMIs:</Text>
                      <Text style={styles.emiSummaryValue}>{selectedLoan.tenure_months}</Text>
                    </View>
                    <View style={styles.emiSummaryRow}>
                      <Text style={styles.emiSummaryLabel}>Paid EMIs:</Text>
                      <Text style={[styles.emiSummaryValue, { color: Colors.success }]}>
                        {selectedLoan.paidEMIs}
                      </Text>
                    </View>
                    <View style={styles.emiSummaryRow}>
                      <Text style={styles.emiSummaryLabel}>Pending EMIs:</Text>
                      <Text style={[styles.emiSummaryValue, { color: Colors.error }]}>
                        {selectedLoan.unpaidEMIs}
                      </Text>
                    </View>
                  </Card>
                }
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
  },
  addButton: {
    backgroundColor: Colors.primary[600],
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summarySection: {
    padding: Spacing['2xl'],
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  summaryCard: {
    flex: 1,
    padding: Spacing.md,
  },
  summaryCardContent: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  loansSection: {
    paddingHorizontal: Spacing['2xl'],
    paddingBottom: Spacing['2xl'],
  },
  loanCard: {
    marginBottom: Spacing.md,
  },
  loanContent: {
    gap: Spacing.md,
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  loanName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    flex: 1,
  },
  loanAmount: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.primary[600],
  },
  loanDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loanDetailItem: {
    alignItems: 'center',
  },
  loanDetailLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  loanDetailValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary[600],
  },
  progressText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.secondary,
  },
  loanActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
    gap: Spacing.xs,
  },
  actionButtonText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary[600],
    fontWeight: Typography.fontWeight.medium as any,
  },
  emiCard: {
    marginBottom: Spacing.md,
  },
  emiContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emiInfo: {
    flex: 1,
  },
  emiAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  emiDueDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  emiPaymentDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.success,
  },
  emiStatus: {
    alignItems: 'flex-end',
    gap: Spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium as any,
  },
  payButton: {
    backgroundColor: Colors.primary[600],
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: 16,
  },
  payButtonText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.medium as any,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: Spacing['4xl'],
    marginTop: Spacing['2xl'],
  },
  emptyText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.secondary,
    marginTop: Spacing.lg,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.tertiary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  modalTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
  },
  cancelButton: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  saveButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.primary[600],
  },
  modalContent: {
    flex: 1,
    padding: Spacing['2xl'],
  },
  emiPreview: {
    backgroundColor: Colors.primary[50],
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  emiPreviewLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  emiPreviewAmount: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.primary[600],
  },
  emiSummary: {
    marginBottom: Spacing.lg,
  },
  emiSummaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  emiSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  emiSummaryLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  emiSummaryValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
  },
});
