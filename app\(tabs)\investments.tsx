import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Colors, Currencies, InvestmentTypes, Spacing, Typography } from '@/constants/Theme';
import { useAuth } from '@/context/AuthContext';
import {
  addInvestment,
  deleteInvestment,
  getInvestments,
  getInvestmentSummary,
  Investment,
  updateInvestment
} from '@/utils/database';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface InvestmentFormData {
  amount: string;
  type: string;
  ROI: string;
  date: string;
}

interface InvestmentSummary {
  total_invested: number;
  average_roi: number;
  total_investments: number;
}

export default function InvestmentsScreen() {
  const { user } = useAuth();
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [summary, setSummary] = useState<InvestmentSummary>({
    total_invested: 0,
    average_roi: 0,
    total_investments: 0,
  });
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingInvestment, setEditingInvestment] = useState<Investment | null>(null);
  const [formData, setFormData] = useState<InvestmentFormData>({
    amount: '',
    type: InvestmentTypes[0],
    ROI: '',
    date: new Date().toISOString().split('T')[0],
  });

  const loadInvestments = useCallback(async () => {
    if (!user?.id) return;

    try {
      const investmentList = getInvestments(user.id);
      setInvestments(investmentList);

      const summaryData = getInvestmentSummary(user.id);
      setSummary(summaryData || { total_invested: 0, average_roi: 0, total_investments: 0 });
    } catch (error) {
      console.error('Error loading investments:', error);
      Alert.alert('Error', 'Failed to load investments');
    }
  }, [user?.id]);

  useEffect(() => {
    loadInvestments();
  }, [loadInvestments]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInvestments();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    const currency = user?.preferred_currency || 'NRP';
    const symbol = Currencies[currency as keyof typeof Currencies]?.symbol || '₨';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const handleAddInvestment = () => {
    setEditingInvestment(null);
    setFormData({
      amount: '',
      type: InvestmentTypes[0],
      ROI: '',
      date: new Date().toISOString().split('T')[0],
    });
    setModalVisible(true);
  };

  const handleEditInvestment = (investment: Investment) => {
    setEditingInvestment(investment);
    setFormData({
      amount: investment.amount.toString(),
      type: investment.type,
      ROI: investment.ROI?.toString() || '',
      date: investment.date,
    });
    setModalVisible(true);
  };

  const handleDeleteInvestment = (investment: Investment) => {
    Alert.alert(
      'Delete Investment',
      'Are you sure you want to delete this investment?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            try {
              deleteInvestment(investment.id!);
              loadInvestments();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete investment');
            }
          },
        },
      ]
    );
  };

  const handleSaveInvestment = () => {
    if (!user?.id || !formData.amount || !formData.type) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(formData.amount);
    const roi = parseFloat(formData.ROI) || 0;

    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      if (editingInvestment) {
        updateInvestment(
          editingInvestment.id!,
          amount,
          formData.type,
          roi,
          formData.date
        );
      } else {
        addInvestment(
          user.id,
          amount,
          formData.type,
          roi,
          formData.date
        );
      }

      setModalVisible(false);
      loadInvestments();
    } catch (error) {
      Alert.alert('Error', 'Failed to save investment');
    }
  };

  const getInvestmentIcon = (type: string) => {
    switch (type) {
      case 'stocks':
        return 'trending-up';
      case 'mutual_funds':
        return 'pie-chart';
      case 'crypto':
        return 'logo-bitcoin';
      case 'savings':
        return 'wallet';
      default:
        return 'trending-up';
    }
  };

  const getInvestmentColor = (roi: number) => {
    if (roi > 0) return Colors.success;
    if (roi < 0) return Colors.error;
    return Colors.text.secondary;
  };

  const renderInvestmentItem = ({ item }: { item: Investment }) => (
    <Card style={styles.investmentCard}>
      <View style={styles.investmentContent}>
        <View style={styles.investmentInfo}>
          <View style={styles.investmentHeader}>
            <View style={styles.investmentTypeContainer}>
              <Ionicons
                name={getInvestmentIcon(item.type) as any}
                size={20}
                color={Colors.primary[600]}
              />
              <Text style={styles.investmentType}>
                {item.type.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            <Text style={styles.investmentAmount}>{formatCurrency(item.amount)}</Text>
          </View>

          {item.ROI !== undefined && (
            <View style={styles.roiContainer}>
              <Text style={styles.roiLabel}>ROI: </Text>
              <Text style={[styles.roiValue, { color: getInvestmentColor(item.ROI) }]}>
                {formatPercentage(item.ROI)}
              </Text>
            </View>
          )}

          <Text style={styles.investmentDate}>
            {new Date(item.date).toLocaleDateString()}
          </Text>
        </View>

        <View style={styles.investmentActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditInvestment(item)}
          >
            <Ionicons name="pencil" size={16} color={Colors.primary[600]} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteInvestment(item)}
          >
            <Ionicons name="trash" size={16} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Investments</Text>
          <TouchableOpacity style={styles.addButton} onPress={handleAddInvestment}>
            <Ionicons name="add" size={24} color={Colors.text.inverse} />
          </TouchableOpacity>
        </View>

        {/* Portfolio Summary */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Portfolio Summary</Text>

          <View style={styles.summaryCards}>
            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="wallet-outline" size={24} color={Colors.primary[600]} />
                <Text style={styles.summaryValue}>{formatCurrency(summary.total_invested)}</Text>
                <Text style={styles.summaryLabel}>Total Invested</Text>
              </View>
            </Card>

            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="trending-up-outline" size={24} color={Colors.success} />
                <Text style={[styles.summaryValue, { color: getInvestmentColor(summary.average_roi) }]}>
                  {formatPercentage(summary.average_roi)}
                </Text>
                <Text style={styles.summaryLabel}>Avg. ROI</Text>
              </View>
            </Card>

            <Card style={styles.summaryCard}>
              <View style={styles.summaryCardContent}>
                <Ionicons name="pie-chart-outline" size={24} color={Colors.warning} />
                <Text style={styles.summaryValue}>{summary.total_investments}</Text>
                <Text style={styles.summaryLabel}>Investments</Text>
              </View>
            </Card>
          </View>
        </View>

        {/* Investment List */}
        <View style={styles.investmentSection}>
          <Text style={styles.sectionTitle}>Your Investments</Text>

          {investments.length > 0 ? (
            <FlatList
              data={investments}
              renderItem={renderInvestmentItem}
              keyExtractor={(item) => item.id!.toString()}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <Card style={styles.emptyCard}>
              <Ionicons name="trending-up-outline" size={48} color={Colors.text.tertiary} />
              <Text style={styles.emptyText}>No investments yet</Text>
              <Text style={styles.emptySubtext}>Tap the + button to add your first investment</Text>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Add/Edit Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {editingInvestment ? 'Edit Investment' : 'Add Investment'}
            </Text>
            <TouchableOpacity onPress={handleSaveInvestment}>
              <Text style={styles.saveButton}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Input
              label="Amount *"
              value={formData.amount}
              onChangeText={(text) => setFormData({ ...formData, amount: text })}
              placeholder="0.00"
              keyboardType="numeric"
            />

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Investment Type *</Text>
              <View style={styles.typeContainer}>
                {InvestmentTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.typeButton,
                      formData.type === type && styles.typeButtonActive,
                    ]}
                    onPress={() => setFormData({ ...formData, type })}
                  >
                    <Ionicons
                      name={getInvestmentIcon(type) as any}
                      size={20}
                      color={formData.type === type ? Colors.text.inverse : Colors.text.secondary}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        formData.type === type && styles.typeButtonTextActive,
                      ]}
                    >
                      {type.replace('_', ' ').toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <Input
              label="ROI (%)"
              value={formData.ROI}
              onChangeText={(text) => setFormData({ ...formData, ROI: text })}
              placeholder="0.00"
              keyboardType="numeric"
            />

            <Input
              label="Date *"
              value={formData.date}
              onChangeText={(text) => setFormData({ ...formData, date: text })}
              placeholder="YYYY-MM-DD"
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
  },
  addButton: {
    backgroundColor: Colors.primary[600],
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summarySection: {
    padding: Spacing['2xl'],
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  summaryCard: {
    flex: 1,
    padding: Spacing.md,
  },
  summaryCardContent: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
    marginTop: Spacing.sm,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  investmentSection: {
    paddingHorizontal: Spacing['2xl'],
    paddingBottom: Spacing['2xl'],
  },
  investmentCard: {
    marginBottom: Spacing.md,
  },
  investmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  investmentInfo: {
    flex: 1,
  },
  investmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  investmentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary[50],
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
  },
  investmentType: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.primary[600],
    marginLeft: Spacing.xs,
  },
  investmentAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
  },
  roiContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  roiLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  roiValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
  },
  investmentDate: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
  },
  investmentActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    padding: Spacing.sm,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: Spacing['4xl'],
    marginTop: Spacing['2xl'],
  },
  emptyText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.secondary,
    marginTop: Spacing.lg,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.tertiary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  modalTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
  },
  cancelButton: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  saveButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.primary[600],
  },
  modalContent: {
    flex: 1,
    padding: Spacing['2xl'],
  },
  inputContainer: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border.medium,
    backgroundColor: Colors.background.primary,
    gap: Spacing.xs,
  },
  typeButtonActive: {
    backgroundColor: Colors.primary[600],
    borderColor: Colors.primary[600],
  },
  typeButtonText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
  },
  typeButtonTextActive: {
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.medium as any,
  },
});
