{"expo": {"name": "expense-manager", "slug": "expense-manager", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "expensemanager", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.ashishkamat.expensemanager"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "ac4d73f1-467d-41ca-b926-538af00207c2"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}}