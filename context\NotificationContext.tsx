import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as Notifications from 'expo-notifications';
import { AppState, AppStateStatus } from 'react-native';
import { useAuth } from './AuthContext';
import EMINotificationService, { NotificationPermissions } from '../utils/notificationService';

interface NotificationContextType {
  permissions: NotificationPermissions;
  requestPermissions: () => Promise<void>;
  scheduleEMINotifications: (emiId: number, loanName: string) => Promise<void>;
  cancelEMINotifications: (emiId: number) => Promise<void>;
  rescheduleAllNotifications: () => Promise<void>;
  isNotificationEnabled: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [permissions, setPermissions] = useState<NotificationPermissions>({
    granted: false,
    canAskAgain: true,
  });
  const [isNotificationEnabled, setIsNotificationEnabled] = useState(false);

  useEffect(() => {
    initializeNotifications();
    setupNotificationListeners();
    setupAppStateListener();
  }, []);

  useEffect(() => {
    if (isAuthenticated && user?.id && permissions.granted) {
      // Reschedule notifications when user logs in
      rescheduleAllNotifications();
    }
  }, [isAuthenticated, user?.id, permissions.granted]);

  const initializeNotifications = async () => {
    try {
      const perms = await EMINotificationService.requestPermissions();
      setPermissions(perms);
      setIsNotificationEnabled(perms.granted);
    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  };

  const setupNotificationListeners = () => {
    // Listen for notification responses (when user taps on notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        EMINotificationService.handleNotificationResponse(response);
      }
    );

    // Listen for notifications received while app is in foreground
    const receivedSubscription = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received in foreground:', notification);
      }
    );

    return () => {
      responseSubscription.remove();
      receivedSubscription.remove();
    };
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && isAuthenticated && user?.id && permissions.granted) {
        // Reschedule notifications when app becomes active
        // This ensures notifications are up to date if the app was closed for a while
        rescheduleAllNotifications();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const requestPermissions = async (): Promise<void> => {
    try {
      const perms = await EMINotificationService.requestPermissions();
      setPermissions(perms);
      setIsNotificationEnabled(perms.granted);
      
      if (perms.granted && isAuthenticated && user?.id) {
        await rescheduleAllNotifications();
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
    }
  };

  const scheduleEMINotifications = async (emiId: number, loanName: string): Promise<void> => {
    if (!permissions.granted || !user?.id) return;

    try {
      const { getEMIs } = await import('../utils/database');
      const emis = getEMIs(emiId);
      const emi = emis.find(e => e.id === emiId);
      
      if (emi) {
        await EMINotificationService.scheduleEMINotifications(emi, loanName);
      }
    } catch (error) {
      console.error('Error scheduling EMI notifications:', error);
    }
  };

  const cancelEMINotifications = async (emiId: number): Promise<void> => {
    try {
      await EMINotificationService.cancelEMINotifications(emiId);
    } catch (error) {
      console.error('Error canceling EMI notifications:', error);
    }
  };

  const rescheduleAllNotifications = async (): Promise<void> => {
    if (!permissions.granted || !user?.id) return;

    try {
      await EMINotificationService.rescheduleAllNotifications(user.id);
    } catch (error) {
      console.error('Error rescheduling all notifications:', error);
    }
  };

  const value: NotificationContextType = {
    permissions,
    requestPermissions,
    scheduleEMINotifications,
    cancelEMINotifications,
    rescheduleAllNotifications,
    isNotificationEnabled,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
