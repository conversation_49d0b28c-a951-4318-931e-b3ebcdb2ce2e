import * as SQLite from 'expo-sqlite';

const db = SQLite.openDatabaseSync('financeTracker.db');

// Database Types
export interface User {
  id?: number;
  username: string;
  password: string;
  preferred_currency: string;
  created_at?: string;
}

export interface Expense {
  id?: number;
  user_id: number;
  amount: number;
  category: string;
  description?: string;
  date: string;
  created_at?: string;
}

export interface Investment {
  id?: number;
  user_id: number;
  amount: number;
  type: 'stocks' | 'mutual_funds' | 'crypto' | 'savings';
  ROI?: number;
  date: string;
  created_at?: string;
}

export interface Loan {
  id?: number;
  user_id: number;
  name: string;
  principal_amount: number;
  interest_rate: number;
  start_date: string;
  tenure_months: number;
  created_at?: string;
}

export interface EMI {
  id?: number;
  loan_id: number;
  amount: number;
  due_date: string;
  status: 'paid' | 'unpaid';
  payment_date?: string;
  last_notification_date?: string;
  created_at?: string;
}

// Initialize database tables
export const initDatabase = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // Users table
      db.execSync(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          preferred_currency TEXT DEFAULT 'NRP',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Expenses table
      db.execSync(`
        CREATE TABLE IF NOT EXISTS expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          category TEXT NOT NULL,
          description TEXT,
          date TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        );
      `);

      // Investments table
      db.execSync(`
        CREATE TABLE IF NOT EXISTS investments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          type TEXT NOT NULL CHECK(type IN ('stocks', 'mutual_funds', 'crypto', 'savings')),
          ROI REAL,
          date TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        );
      `);

      // Loans table
      db.execSync(`
        CREATE TABLE IF NOT EXISTS loans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          principal_amount REAL NOT NULL,
          interest_rate REAL NOT NULL,
          start_date TEXT NOT NULL,
          tenure_months INTEGER NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        );
      `);

      // EMIs table
      db.execSync(`
        CREATE TABLE IF NOT EXISTS emis (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          loan_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          due_date TEXT NOT NULL,
          status TEXT DEFAULT 'unpaid' CHECK(status IN ('paid', 'unpaid')),
          payment_date TEXT,
          last_notification_date TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (loan_id) REFERENCES loans (id) ON DELETE CASCADE
        );
      `);

      console.log('Database initialized successfully');
      resolve();
    } catch (error) {
      console.error('Database initialization error:', error);
      reject(error);
    }
  });
};

// ============= USER OPERATIONS =============

export const createUser = (username: string, password: string, preferredCurrency: string = 'NRP'): number => {
  try {
    const result = db.runSync(
      'INSERT INTO users (username, password, preferred_currency) VALUES (?, ?, ?)',
      [username, password, preferredCurrency]
    );
    return result.lastInsertRowId;
  } catch (error) {
    throw new Error(`Failed to create user: ${error}`);
  }
};

export const getUserByUsername = (username: string): User | null => {
  try {
    const result = db.getFirstSync('SELECT * FROM users WHERE username = ?', [username]);
    return result as User | null;
  } catch (error) {
    throw new Error(`Failed to get user: ${error}`);
  }
};

export const updateUserCurrency = (userId: number, currency: string): void => {
  try {
    db.runSync('UPDATE users SET preferred_currency = ? WHERE id = ?', [currency, userId]);
  } catch (error) {
    throw new Error(`Failed to update user currency: ${error}`);
  }
};

// ============= EXPENSE OPERATIONS =============

export const addExpense = (userId: number, amount: number, category: string, description: string, date: string): number => {
  try {
    const result = db.runSync(
      'INSERT INTO expenses (user_id, amount, category, description, date) VALUES (?, ?, ?, ?, ?)',
      [userId, amount, category, description, date]
    );
    return result.lastInsertRowId;
  } catch (error) {
    throw new Error(`Failed to add expense: ${error}`);
  }
};

export const getExpenses = (userId: number): Expense[] => {
  try {
    const result = db.getAllSync('SELECT * FROM expenses WHERE user_id = ? ORDER BY date DESC', [userId]);
    return result as Expense[];
  } catch (error) {
    throw new Error(`Failed to get expenses: ${error}`);
  }
};

export const updateExpense = (id: number, amount: number, category: string, description: string, date: string): void => {
  try {
    db.runSync(
      'UPDATE expenses SET amount = ?, category = ?, description = ?, date = ? WHERE id = ?',
      [amount, category, description, date, id]
    );
  } catch (error) {
    throw new Error(`Failed to update expense: ${error}`);
  }
};

export const deleteExpense = (id: number): void => {
  try {
    db.runSync('DELETE FROM expenses WHERE id = ?', [id]);
  } catch (error) {
    throw new Error(`Failed to delete expense: ${error}`);
  }
};

// ============= INVESTMENT OPERATIONS =============

export const addInvestment = (userId: number, amount: number, type: string, ROI: number, date: string): number => {
  try {
    const result = db.runSync(
      'INSERT INTO investments (user_id, amount, type, ROI, date) VALUES (?, ?, ?, ?, ?)',
      [userId, amount, type, ROI, date]
    );
    return result.lastInsertRowId;
  } catch (error) {
    throw new Error(`Failed to add investment: ${error}`);
  }
};

export const getInvestments = (userId: number): Investment[] => {
  try {
    const result = db.getAllSync('SELECT * FROM investments WHERE user_id = ? ORDER BY date DESC', [userId]);
    return result as Investment[];
  } catch (error) {
    throw new Error(`Failed to get investments: ${error}`);
  }
};

export const updateInvestment = (id: number, amount: number, type: string, ROI: number, date: string): void => {
  try {
    db.runSync(
      'UPDATE investments SET amount = ?, type = ?, ROI = ?, date = ? WHERE id = ?',
      [amount, type, ROI, date, id]
    );
  } catch (error) {
    throw new Error(`Failed to update investment: ${error}`);
  }
};

export const deleteInvestment = (id: number): void => {
  try {
    db.runSync('DELETE FROM investments WHERE id = ?', [id]);
  } catch (error) {
    throw new Error(`Failed to delete investment: ${error}`);
  }
};

// ============= LOAN OPERATIONS =============

export const addLoan = (userId: number, name: string, principalAmount: number, interestRate: number, startDate: string, tenureMonths: number): number => {
  try {
    const result = db.runSync(
      'INSERT INTO loans (user_id, name, principal_amount, interest_rate, start_date, tenure_months) VALUES (?, ?, ?, ?, ?, ?)',
      [userId, name, principalAmount, interestRate, startDate, tenureMonths]
    );

    const loanId = result.lastInsertRowId;

    // Calculate EMI amount using formula: P * r * (1 + r)^n / ((1 + r)^n - 1)
    const monthlyRate = interestRate / 100 / 12;
    const emiAmount = (principalAmount * monthlyRate * Math.pow(1 + monthlyRate, tenureMonths)) /
                      (Math.pow(1 + monthlyRate, tenureMonths) - 1);

    // Generate EMIs for the loan
    const startDateObj = new Date(startDate);
    for (let i = 0; i < tenureMonths; i++) {
      const dueDate = new Date(startDateObj);
      dueDate.setMonth(dueDate.getMonth() + i + 1);

      db.runSync(
        'INSERT INTO emis (loan_id, amount, due_date) VALUES (?, ?, ?)',
        [loanId, Math.round(emiAmount * 100) / 100, dueDate.toISOString().split('T')[0]]
      );
    }

    return loanId;
  } catch (error) {
    throw new Error(`Failed to add loan: ${error}`);
  }
};

export const getLoans = (userId: number): Loan[] => {
  try {
    const result = db.getAllSync('SELECT * FROM loans WHERE user_id = ? ORDER BY start_date DESC', [userId]);
    return result as Loan[];
  } catch (error) {
    throw new Error(`Failed to get loans: ${error}`);
  }
};

export const updateLoan = (id: number, name: string, principalAmount: number, interestRate: number, startDate: string, tenureMonths: number): void => {
  try {
    db.runSync(
      'UPDATE loans SET name = ?, principal_amount = ?, interest_rate = ?, start_date = ?, tenure_months = ? WHERE id = ?',
      [name, principalAmount, interestRate, startDate, tenureMonths, id]
    );
  } catch (error) {
    throw new Error(`Failed to update loan: ${error}`);
  }
};

export const deleteLoan = (id: number): void => {
  try {
    // Delete associated EMIs first (CASCADE should handle this, but being explicit)
    db.runSync('DELETE FROM emis WHERE loan_id = ?', [id]);
    db.runSync('DELETE FROM loans WHERE id = ?', [id]);
  } catch (error) {
    throw new Error(`Failed to delete loan: ${error}`);
  }
};

// ============= EMI OPERATIONS =============

export const getEMIs = (loanId: number): EMI[] => {
  try {
    const result = db.getAllSync('SELECT * FROM emis WHERE loan_id = ? ORDER BY due_date ASC', [loanId]);
    return result as EMI[];
  } catch (error) {
    throw new Error(`Failed to get EMIs: ${error}`);
  }
};

export const getUpcomingEMIs = (userId: number, daysAhead: number = 30): EMI[] => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);
    const futureDateStr = futureDate.toISOString().split('T')[0];

    const result = db.getAllSync(`
      SELECT e.*, l.name as loan_name
      FROM emis e
      JOIN loans l ON e.loan_id = l.id
      WHERE l.user_id = ? AND e.status = 'unpaid'
        AND e.due_date BETWEEN ? AND ?
      ORDER BY e.due_date ASC
    `, [userId, today, futureDateStr]);

    return result as EMI[];
  } catch (error) {
    throw new Error(`Failed to get upcoming EMIs: ${error}`);
  }
};

export const markEMIAsPaid = (emiId: number, paymentDate: string): void => {
  try {
    db.runSync(
      'UPDATE emis SET status = ?, payment_date = ? WHERE id = ?',
      ['paid', paymentDate, emiId]
    );
  } catch (error) {
    throw new Error(`Failed to mark EMI as paid: ${error}`);
  }
};

export const updateEMINotificationDate = (emiId: number, notificationDate: string): void => {
  try {
    db.runSync(
      'UPDATE emis SET last_notification_date = ? WHERE id = ?',
      [notificationDate, emiId]
    );
  } catch (error) {
    throw new Error(`Failed to update EMI notification date: ${error}`);
  }
};

// ============= ANALYTICS OPERATIONS =============

export const getExpensesByCategory = (userId: number, startDate: string, endDate: string): any[] => {
  try {
    const result = db.getAllSync(`
      SELECT category, SUM(amount) as total_amount, COUNT(*) as count
      FROM expenses
      WHERE user_id = ? AND date BETWEEN ? AND ?
      GROUP BY category
      ORDER BY total_amount DESC
    `, [userId, startDate, endDate]);
    return result;
  } catch (error) {
    throw new Error(`Failed to get expenses by category: ${error}`);
  }
};

export const getExpensesByTimeframe = (userId: number, timeframe: 'week' | 'month' | 'year'): any[] => {
  try {
    let dateFormat = '';
    switch (timeframe) {
      case 'week':
        dateFormat = "strftime('%Y-%W', date)";
        break;
      case 'month':
        dateFormat = "strftime('%Y-%m', date)";
        break;
      case 'year':
        dateFormat = "strftime('%Y', date)";
        break;
    }

    const result = db.getAllSync(`
      SELECT ${dateFormat} as period, SUM(amount) as total_amount, COUNT(*) as count
      FROM expenses
      WHERE user_id = ?
      GROUP BY period
      ORDER BY period DESC
      LIMIT 12
    `, [userId]);
    return result;
  } catch (error) {
    throw new Error(`Failed to get expenses by timeframe: ${error}`);
  }
};

export const getInvestmentSummary = (userId: number): any => {
  try {
    const result = db.getFirstSync(`
      SELECT
        SUM(amount) as total_invested,
        AVG(ROI) as average_roi,
        COUNT(*) as total_investments
      FROM investments
      WHERE user_id = ?
    `, [userId]);
    return result;
  } catch (error) {
    throw new Error(`Failed to get investment summary: ${error}`);
  }
};

export const getLoanSummary = (userId: number): any => {
  try {
    const result = db.getFirstSync(`
      SELECT
        COUNT(*) as total_loans,
        SUM(principal_amount) as total_principal,
        (SELECT COUNT(*) FROM emis e JOIN loans l ON e.loan_id = l.id WHERE l.user_id = ? AND e.status = 'unpaid') as pending_emis
      FROM loans
      WHERE user_id = ?
    `, [userId, userId]);
    return result;
  } catch (error) {
    throw new Error(`Failed to get loan summary: ${error}`);
  }
};

export const getDashboardData = (userId: number): any => {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const monthlyExpenses = db.getFirstSync(`
      SELECT SUM(amount) as total
      FROM expenses
      WHERE user_id = ? AND strftime('%Y-%m', date) = ?
    `, [userId, currentMonth]) as { total: number } | null;

    const totalInvestments = db.getFirstSync(`
      SELECT SUM(amount) as total
      FROM investments
      WHERE user_id = ?
    `, [userId]) as { total: number } | null;

    const upcomingEMIs = getUpcomingEMIs(userId, 7); // Next 7 days

    return {
      monthlyExpenses: monthlyExpenses?.total || 0,
      totalInvestments: totalInvestments?.total || 0,
      upcomingEMIs: upcomingEMIs.length
    };
  } catch (error) {
    throw new Error(`Failed to get dashboard data: ${error}`);
  }
};