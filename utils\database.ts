import * as SQLite from 'expo-sqlite';

const db = SQLite.openDatabase('expenseTracker.db');

// Database Types
export interface Expense {
  id?: number;
  amount: number;
  category: string;
  description?: string;
  date: string;
  payment_method?: string;
  created_at?: string;
}

export interface EmiLoan {
  id?: number;
  name: string;
  principal_amount: number;
  remaining_principal: number;
  interest_rate: number;
  tenure_months: number;
  emi_amount: number;
  start_date: string;
  end_date: string;
  next_payment_date: string;
  status?: 'active' | 'completed';
  created_at?: string;
}

export interface EmiPayment {
  id?: number;
  loan_id: number;
  payment_amount: number;
  payment_date: string;
  payment_type: 'emi' | 'prepayment';
  principal_component?: number;
  interest_component?: number;
  remaining_balance_after?: number;
  notes?: string;
}

export interface Investment {
  id?: number;
  type: 'stock' | 'mutual_fund' | 'fd' | 'crypto';
  name: string;
  amount_invested: number;
  current_value?: number;
  purchase_date: string;
  additional_details?: string;
  created_at?: string;
}

// Initialize database tables
export const initDatabase = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      // Expenses table
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          amount REAL NOT NULL,
          category TEXT NOT NULL,
          description TEXT,
          date TEXT NOT NULL,
          payment_method TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        );`
      );

      // Enhanced EMIs/Loans table
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS emis_loans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          principal_amount REAL NOT NULL,
          remaining_principal REAL NOT NULL,
          interest_rate REAL NOT NULL,
          tenure_months INTEGER NOT NULL,
          emi_amount REAL NOT NULL,
          start_date TEXT NOT NULL,
          end_date TEXT NOT NULL,
          next_payment_date TEXT NOT NULL,
          status TEXT DEFAULT 'active',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        );`
      );

      // New EMI Payments table
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS emi_payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          loan_id INTEGER NOT NULL,
          payment_amount REAL NOT NULL,
          payment_date TEXT NOT NULL,
          payment_type TEXT NOT NULL CHECK(payment_type IN ('emi', 'prepayment')),
          principal_component REAL,
          interest_component REAL,
          remaining_balance_after REAL,
          notes TEXT,
          FOREIGN KEY (loan_id) REFERENCES emis_loans (id) ON DELETE CASCADE
        );`
      );

      // Investments table
      tx.executeSql(
        `CREATE TABLE IF NOT EXISTS investments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          type TEXT NOT NULL CHECK(type IN ('stock', 'mutual_fund', 'fd', 'crypto')),
          name TEXT NOT NULL,
          amount_invested REAL NOT NULL,
          current_value REAL,
          purchase_date TEXT NOT NULL,
          additional_details TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        );`
      );
    }, 
    (error: SQLite.SQLError) => {
      console.error('Database initialization error:', error);
      reject(error);
    }, 
    () => {
      console.log('Database initialized successfully');
      resolve();
    });
  });
};

// ============= ENHANCED EMIS/LOANS OPERATIONS =============

export const addEmiLoan = (
  name: string,
  principalAmount: number,
  interestRate: number,
  tenureMonths: number,
  emiAmount: number,
  startDate: string,
  endDate: string
): Promise<number> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `INSERT INTO emis_loans 
         (name, principal_amount, remaining_principal, interest_rate, tenure_months, emi_amount, start_date, end_date, next_payment_date) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);`,
        [name, principalAmount, principalAmount, interestRate, tenureMonths, emiAmount, startDate, endDate, startDate],
        (_, { insertId }) => resolve(insertId as number),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

export const recordEmiPayment = (
  loanId: number,
  paymentAmount: number,
  paymentDate: string,
  paymentType: 'emi' | 'prepayment',
  notes: string = ''
): Promise<number> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT * FROM emis_loans WHERE id = ?;`,
        [loanId],
        (_, { rows }) => {
          if (rows.length === 0) {
            reject(new Error('Loan not found'));
            return;
          }
          
          const loan = rows.item(0) as EmiLoan;
          const remainingPrincipal = loan.remaining_principal;
          const monthlyInterestRate = loan.interest_rate / 100 / 12;
          
          const interestComponent = remainingPrincipal * monthlyInterestRate;
          const principalComponent = paymentAmount - interestComponent;
          
          const actualPrincipalComponent = Math.min(principalComponent, remainingPrincipal);
          const actualInterestComponent = paymentAmount - actualPrincipalComponent;
          
          const newRemainingPrincipal = remainingPrincipal - actualPrincipalComponent;
          
          let nextPaymentDate = loan.next_payment_date;
          if (paymentType === 'emi') {
            const currentDate = new Date(paymentDate);
            currentDate.setMonth(currentDate.getMonth() + 1);
            nextPaymentDate = currentDate.toISOString().split('T')[0];
          }
          
          tx.executeSql(
            `UPDATE emis_loans 
             SET remaining_principal = ?, next_payment_date = ?, status = ? 
             WHERE id = ?;`,
            [
              newRemainingPrincipal,
              nextPaymentDate,
              newRemainingPrincipal <= 0 ? 'completed' : 'active',
              loanId
            ],
            (_, { rowsAffected }) => {
              if (rowsAffected === 0) {
                reject(new Error('Failed to update loan'));
                return;
              }
              
              tx.executeSql(
                `INSERT INTO emi_payments 
                 (loan_id, payment_amount, payment_date, payment_type, principal_component, interest_component, remaining_balance_after, notes) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?);`,
                [
                  loanId,
                  paymentAmount,
                  paymentDate,
                  paymentType,
                  actualPrincipalComponent,
                  actualInterestComponent,
                  newRemainingPrincipal,
                  notes
                ],
                (_, { insertId }) => resolve(insertId as number),
                (_, error) => {
                  reject(error);
                  return false;
                }
              );
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        },
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

export const getEmisLoans = (): Promise<EmiLoan[]> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT 
           l.*,
           (SELECT COUNT(*) FROM emi_payments WHERE loan_id = l.id) as total_payments,
           (SELECT SUM(payment_amount) FROM emi_payments WHERE loan_id = l.id) as total_paid
         FROM emis_loans l
         ORDER BY l.start_date DESC;`,
        [],
        (_, { rows }) => resolve(rows._array as EmiLoan[]),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

export const getLoanPaymentHistory = (loanId: number): Promise<EmiPayment[]> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT * FROM emi_payments 
         WHERE loan_id = ? 
         ORDER BY payment_date DESC;`,
        [loanId],
        (_, { rows }) => resolve(rows._array as EmiPayment[]),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

export const getUpcomingEmiPayments = (daysAhead: number = 30): Promise<EmiLoan[]> => {
  return new Promise((resolve, reject) => {
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);
    const futureDateStr = futureDate.toISOString().split('T')[0];
    
    db.transaction(tx => {
      tx.executeSql(
        `SELECT 
           l.*,
           (SELECT SUM(payment_amount) FROM emi_payments WHERE loan_id = l.id) as total_paid
         FROM emis_loans l
         WHERE l.status = 'active' 
           AND l.next_payment_date BETWEEN ? AND ?
         ORDER BY l.next_payment_date ASC;`,
        [today, futureDateStr],
        (_, { rows }) => resolve(rows._array as EmiLoan[]),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

export const getLoanSummary = (loanId: number): Promise<EmiLoan> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT 
           l.*,
           (SELECT COUNT(*) FROM emi_payments WHERE loan_id = l.id) as payments_made,
           (SELECT SUM(payment_amount) FROM emi_payments WHERE loan_id = l.id) as total_paid,
           (SELECT SUM(principal_component) FROM emi_payments WHERE loan_id = l.id) as principal_paid,
           (SELECT SUM(interest_component) FROM emi_payments WHERE loan_id = l.id) as interest_paid
         FROM emis_loans l
         WHERE l.id = ?;`,
        [loanId],
        (_, { rows }) => {
          if (rows.length === 0) {
            reject(new Error('Loan not found'));
            return;
          }
          resolve(rows.item(0) as EmiLoan);
        },
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

// ============= REPORTS OPERATIONS =============

export const getEmiPaymentReport = (startDate: string, endDate: string): Promise<EmiPayment[]> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT 
           p.*,
           l.name as loan_name
         FROM emi_payments p
         JOIN emis_loans l ON p.loan_id = l.id
         WHERE p.payment_date BETWEEN ? AND ?
         ORDER BY p.payment_date DESC;`,
        [startDate, endDate],
        (_, { rows }) => resolve(rows._array as EmiPayment[]),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};

interface LoanProgressReport {
  id: number;
  name: string;
  principal_amount: number;
  remaining_principal: number;
  principal_paid: number;
  total_paid: number;
  total_interest_paid: number;
  progress_percentage: number;
  status: string;
}

export const getLoanProgressReport = (): Promise<LoanProgressReport[]> => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `SELECT 
           l.id,
           l.name,
           l.principal_amount,
           l.remaining_principal,
           (l.principal_amount - l.remaining_principal) as principal_paid,
           (SELECT SUM(payment_amount) FROM emi_payments WHERE loan_id = l.id) as total_paid,
           (SELECT SUM(interest_component) FROM emi_payments WHERE loan_id = l.id) as total_interest_paid,
           ROUND((l.principal_amount - l.remaining_principal) * 100.0 / l.principal_amount, 2) as progress_percentage,
           l.status
         FROM emis_loans l
         ORDER BY l.start_date DESC;`,
        [],
        (_, { rows }) => resolve(rows._array as LoanProgressReport[]),
        (_, error) => {
          reject(error);
          return false;
        }
      );
    });
  });
};