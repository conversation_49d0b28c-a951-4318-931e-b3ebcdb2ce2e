import { Card } from '@/components/ui/Card';
import { Colors, Currencies, Spacing, Typography } from '@/constants/Theme';
import { useAuth } from '@/context/AuthContext';
import { useNotifications } from '@/context/NotificationContext';
import { updateUserCurrency } from '@/utils/database';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function SettingsScreen() {
  const { user, logout } = useAuth();
  const { permissions, requestPermissions, isNotificationEnabled, rescheduleAllNotifications } = useNotifications();
  const [selectedCurrency, setSelectedCurrency] = useState(user?.preferred_currency || 'NRP');
  const [notificationsEnabled, setNotificationsEnabled] = useState(isNotificationEnabled);

  useEffect(() => {
    setNotificationsEnabled(isNotificationEnabled);
  }, [isNotificationEnabled]);

  const handleCurrencyChange = async (currency: string) => {
    if (!user?.id) return;

    try {
      updateUserCurrency(user.id, currency);
      setSelectedCurrency(currency);
      Alert.alert('Success', 'Currency updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update currency');
    }
  };

  const handleNotificationToggle = async (enabled: boolean) => {
    if (enabled && !permissions.granted) {
      await requestPermissions();
      if (permissions.granted) {
        setNotificationsEnabled(true);
        await rescheduleAllNotifications();
      }
    } else {
      setNotificationsEnabled(enabled);
      if (!enabled) {
        // Cancel all notifications when disabled
        const notificationService = await import('@/utils/notificationService');
        await notificationService.default.cancelAllEMINotifications();
      } else {
        await rescheduleAllNotifications();
      }
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.iconContainer}>
          <Ionicons name={icon as any} size={20} color={Colors.primary[600]} />
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightComponent || (onPress && (
        <Ionicons name="chevron-forward" size={20} color={Colors.text.tertiary} />
      ))}
    </TouchableOpacity>
  );

  const renderCurrencySelector = () => (
    <Card style={styles.currencyCard}>
      <Text style={styles.sectionTitle}>Currency</Text>
      <View style={styles.currencyGrid}>
        {Object.entries(Currencies).map(([code, { symbol, name }]) => (
          <TouchableOpacity
            key={code}
            style={[
              styles.currencyOption,
              selectedCurrency === code && styles.currencyOptionSelected,
            ]}
            onPress={() => handleCurrencyChange(code)}
          >
            <Text style={[
              styles.currencySymbol,
              selectedCurrency === code && styles.currencySymbolSelected,
            ]}>
              {symbol}
            </Text>
            <Text style={[
              styles.currencyCode,
              selectedCurrency === code && styles.currencyCodeSelected,
            ]}>
              {code}
            </Text>
            <Text style={[
              styles.currencyName,
              selectedCurrency === code && styles.currencyNameSelected,
            ]}>
              {name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
        </View>

        {/* User Info */}
        <Card style={styles.userCard}>
          <View style={styles.userInfo}>
            <View style={styles.userAvatar}>
              <Ionicons name="person" size={24} color={Colors.primary[600]} />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user?.username}</Text>
              <Text style={styles.userEmail}>Finance Tracker User</Text>
            </View>
          </View>
        </Card>

        {/* Currency Selection */}
        {renderCurrencySelector()}

        {/* Notification Settings */}
        <Card style={styles.settingsCard}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          {renderSettingItem(
            'notifications',
            'EMI Reminders',
            'Get notified about upcoming EMI payments',
            undefined,
            <Switch
              value={notificationsEnabled}
              onValueChange={handleNotificationToggle}
              trackColor={{ false: Colors.gray[300], true: Colors.primary[200] }}
              thumbColor={notificationsEnabled ? Colors.primary[600] : Colors.gray[400]}
            />
          )}
        </Card>

        {/* App Settings */}
        <Card style={styles.settingsCard}>
          <Text style={styles.sectionTitle}>Account</Text>
          {renderSettingItem(
            'log-out',
            'Logout',
            'Sign out of your account',
            handleLogout
          )}
        </Card>

        {/* App Info */}
        <Card style={styles.settingsCard}>
          <Text style={styles.sectionTitle}>About</Text>
          {renderSettingItem(
            'information-circle',
            'Version',
            '1.0.0'
          )}
          {renderSettingItem(
            'shield-checkmark',
            'Privacy Policy',
            'View our privacy policy'
          )}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
  },
  userCard: {
    margin: Spacing['2xl'],
    marginBottom: Spacing.lg,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  currencyCard: {
    margin: Spacing['2xl'],
    marginTop: 0,
    marginBottom: Spacing.lg,
  },
  settingsCard: {
    margin: Spacing['2xl'],
    marginTop: 0,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
  },
  currencyGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  currencyOption: {
    flex: 1,
    minWidth: '30%',
    padding: Spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border.light,
    backgroundColor: Colors.background.primary,
    alignItems: 'center',
  },
  currencyOptionSelected: {
    borderColor: Colors.primary[600],
    backgroundColor: Colors.primary[50],
  },
  currencySymbol: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  currencySymbolSelected: {
    color: Colors.primary[600],
  },
  currencyCode: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  currencyCodeSelected: {
    color: Colors.primary[600],
  },
  currencyName: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  currencyNameSelected: {
    color: Colors.primary[700],
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium as any,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  settingSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
});
