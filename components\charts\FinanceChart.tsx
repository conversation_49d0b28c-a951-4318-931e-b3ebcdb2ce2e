import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> } from 'react-native-chart-kit';
import { Colors, Typography, Spacing } from '@/constants/Theme';

const screenWidth = Dimensions.get('window').width;

interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

interface PieChartData {
  name: string;
  amount: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

interface FinanceChartProps {
  type: 'line' | 'bar' | 'pie';
  data: ChartData | PieChartData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
}

const chartConfig = {
  backgroundColor: Colors.background.primary,
  backgroundGradientFrom: Colors.background.primary,
  backgroundGradientTo: Colors.background.primary,
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(37, 99, 235, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: Colors.primary[600],
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
    stroke: Colors.border.light,
  },
};

export const FinanceChart: React.FC<FinanceChartProps> = ({
  type,
  data,
  title,
  height = 220,
  showLegend = false,
}) => {
  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart
            data={data as ChartData}
            width={screenWidth - 32}
            height={height}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
            withInnerLines={false}
            withOuterLines={false}
            withVerticalLines={false}
            withHorizontalLines={true}
            withDots={true}
            withShadow={false}
          />
        );
      
      case 'bar':
        return (
          <BarChart
            data={data as ChartData}
            width={screenWidth - 32}
            height={height}
            chartConfig={chartConfig}
            style={styles.chart}
            withInnerLines={false}
            withVerticalLines={false}
            withHorizontalLines={true}
            showBarTops={false}
            fromZero={true}
          />
        );
      
      case 'pie':
        return (
          <PieChart
            data={data as PieChartData[]}
            width={screenWidth - 32}
            height={height}
            chartConfig={chartConfig}
            accessor="amount"
            backgroundColor="transparent"
            paddingLeft="15"
            center={[10, 10]}
            style={styles.chart}
            hasLegend={showLegend}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title}</Text>}
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>
    </View>
  );
};

// Utility functions for chart data formatting
export const formatExpenseChartData = (expenses: any[], timeframe: 'week' | 'month' | 'year'): ChartData => {
  const groupedData: { [key: string]: number } = {};
  
  expenses.forEach(expense => {
    const date = new Date(expense.date);
    let key: string;
    
    switch (timeframe) {
      case 'week':
        // Group by day of week
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        key = dayNames[date.getDay()];
        break;
      case 'month':
        // Group by week of month
        const weekOfMonth = Math.ceil(date.getDate() / 7);
        key = `Week ${weekOfMonth}`;
        break;
      case 'year':
        // Group by month
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        key = monthNames[date.getMonth()];
        break;
      default:
        key = date.toDateString();
    }
    
    groupedData[key] = (groupedData[key] || 0) + expense.amount;
  });
  
  const labels = Object.keys(groupedData);
  const data = Object.values(groupedData);
  
  return {
    labels,
    datasets: [{
      data,
      color: (opacity = 1) => `rgba(239, 68, 68, ${opacity})`, // Red for expenses
      strokeWidth: 2,
    }],
  };
};

export const formatInvestmentChartData = (investments: any[]): ChartData => {
  const groupedData: { [key: string]: number } = {};
  
  investments.forEach(investment => {
    const date = new Date(investment.date);
    const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
    groupedData[monthYear] = (groupedData[monthYear] || 0) + investment.amount;
  });
  
  const labels = Object.keys(groupedData).slice(-6); // Last 6 months
  const data = labels.map(label => groupedData[label]);
  
  return {
    labels,
    datasets: [{
      data,
      color: (opacity = 1) => `rgba(16, 185, 129, ${opacity})`, // Green for investments
      strokeWidth: 2,
    }],
  };
};

export const formatCategoryPieData = (expenses: any[]): PieChartData[] => {
  const categoryData: { [key: string]: number } = {};
  
  expenses.forEach(expense => {
    categoryData[expense.category] = (categoryData[expense.category] || 0) + expense.amount;
  });
  
  const colors = [
    Colors.primary[600],
    Colors.success,
    Colors.warning,
    Colors.error,
    Colors.gray[500],
  ];
  
  return Object.entries(categoryData).map(([category, amount], index) => ({
    name: category,
    amount,
    color: colors[index % colors.length],
    legendFontColor: Colors.text.secondary,
    legendFontSize: 12,
  }));
};

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.md,
  },
  title: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
    borderRadius: 16,
    padding: Spacing.sm,
  },
  chart: {
    borderRadius: 16,
  },
});

export default FinanceChart;
