import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { getEMIs, updateEMINotificationDate, EMI } from './database';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationPermissions {
  granted: boolean;
  canAskAgain: boolean;
}

export class EMINotificationService {
  private static instance: EMINotificationService;

  public static getInstance(): EMINotificationService {
    if (!EMINotificationService.instance) {
      EMINotificationService.instance = new EMINotificationService();
    }
    return EMINotificationService.instance;
  }

  /**
   * Request notification permissions from the user
   */
  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('emi-reminders', {
          name: 'EMI Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#2563eb',
        });
      }

      return {
        granted: finalStatus === 'granted',
        canAskAgain: existingStatus !== 'denied',
      };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return { granted: false, canAskAgain: false };
    }
  }

  /**
   * Schedule notifications for a specific EMI
   * Schedules notifications 3 days before, 2 days before, 1 day before, and on due date
   */
  async scheduleEMINotifications(emi: EMI, loanName: string): Promise<void> {
    try {
      const dueDate = new Date(emi.due_date);
      const today = new Date();
      
      // Only schedule if EMI is unpaid and due date is in the future
      if (emi.status === 'paid' || dueDate <= today) {
        return;
      }

      // Calculate notification dates
      const notificationDates = [
        { days: 3, title: 'EMI Due in 3 Days' },
        { days: 2, title: 'EMI Due in 2 Days' },
        { days: 1, title: 'EMI Due Tomorrow' },
        { days: 0, title: 'EMI Due Today' },
      ];

      for (const notification of notificationDates) {
        const notificationDate = new Date(dueDate);
        notificationDate.setDate(dueDate.getDate() - notification.days);
        notificationDate.setHours(9, 0, 0, 0); // 9:00 AM

        // Only schedule if notification date is in the future
        if (notificationDate > today) {
          await Notifications.scheduleNotificationAsync({
            identifier: `emi-${emi.id}-${notification.days}`,
            content: {
              title: notification.title,
              body: `Your EMI payment of ₨${emi.amount.toLocaleString()} for ${loanName} is due on ${dueDate.toLocaleDateString()}. Tap to mark as paid.`,
              data: {
                emiId: emi.id,
                loanName,
                amount: emi.amount,
                dueDate: emi.due_date,
                type: 'emi-reminder',
              },
              sound: true,
              priority: Notifications.AndroidNotificationPriority.HIGH,
            },
            trigger: {
              date: notificationDate,
              channelId: 'emi-reminders',
            },
          });
        }
      }
    } catch (error) {
      console.error('Error scheduling EMI notifications:', error);
    }
  }

  /**
   * Schedule daily notifications for overdue EMIs
   */
  async scheduleOverdueNotifications(emi: EMI, loanName: string): Promise<void> {
    try {
      const dueDate = new Date(emi.due_date);
      const today = new Date();
      
      // Only schedule if EMI is unpaid and overdue
      if (emi.status === 'paid' || dueDate >= today) {
        return;
      }

      const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Schedule next notification for tomorrow at 9 AM
      const nextNotification = new Date();
      nextNotification.setDate(today.getDate() + 1);
      nextNotification.setHours(9, 0, 0, 0);

      await Notifications.scheduleNotificationAsync({
        identifier: `emi-overdue-${emi.id}`,
        content: {
          title: 'Overdue EMI Payment',
          body: `Your EMI payment of ₨${emi.amount.toLocaleString()} for ${loanName} is ${daysPastDue} days overdue. Please pay immediately.`,
          data: {
            emiId: emi.id,
            loanName,
            amount: emi.amount,
            dueDate: emi.due_date,
            daysPastDue,
            type: 'emi-overdue',
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: nextNotification,
          channelId: 'emi-reminders',
        },
      });

      // Update last notification date
      updateEMINotificationDate(emi.id!, today.toISOString().split('T')[0]);
    } catch (error) {
      console.error('Error scheduling overdue notifications:', error);
    }
  }

  /**
   * Cancel all notifications for a specific EMI
   */
  async cancelEMINotifications(emiId: number): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      
      const emiNotifications = scheduledNotifications.filter(notification => 
        notification.identifier.startsWith(`emi-${emiId}`) ||
        notification.identifier === `emi-overdue-${emiId}`
      );

      for (const notification of emiNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Error canceling EMI notifications:', error);
    }
  }

  /**
   * Cancel all EMI notifications
   */
  async cancelAllEMINotifications(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      
      const emiNotifications = scheduledNotifications.filter(notification => 
        notification.identifier.startsWith('emi-')
      );

      for (const notification of emiNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Error canceling all EMI notifications:', error);
    }
  }

  /**
   * Reschedule all notifications for unpaid EMIs
   * This should be called when the app starts or when EMI data changes
   */
  async rescheduleAllNotifications(userId: number): Promise<void> {
    try {
      // Cancel all existing EMI notifications
      await this.cancelAllEMINotifications();

      // Get all loans and their EMIs for the user
      const { getLoans } = await import('./database');
      const loans = getLoans(userId);

      for (const loan of loans) {
        const emis = getEMIs(loan.id!);
        const unpaidEMIs = emis.filter(emi => emi.status === 'unpaid');

        for (const emi of unpaidEMIs) {
          const dueDate = new Date(emi.due_date);
          const today = new Date();

          if (dueDate > today) {
            // Schedule regular notifications
            await this.scheduleEMINotifications(emi, loan.name);
          } else {
            // Schedule overdue notifications
            await this.scheduleOverdueNotifications(emi, loan.name);
          }
        }
      }
    } catch (error) {
      console.error('Error rescheduling notifications:', error);
    }
  }

  /**
   * Handle notification response (when user taps on notification)
   */
  async handleNotificationResponse(response: Notifications.NotificationResponse): Promise<void> {
    try {
      const { data } = response.notification.request.content;
      
      if (data.type === 'emi-reminder' || data.type === 'emi-overdue') {
        // You can implement navigation to EMI screen or show a modal here
        console.log('EMI notification tapped:', data);
        
        // For now, we'll just log the action
        // In a real app, you might want to navigate to the EMI details screen
        // or show a quick action modal to mark the EMI as paid
      }
    } catch (error) {
      console.error('Error handling notification response:', error);
    }
  }

  /**
   * Get all scheduled EMI notifications
   */
  async getScheduledEMINotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      return scheduledNotifications.filter(notification => 
        notification.identifier.startsWith('emi-')
      );
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }
}

export default EMINotificationService.getInstance();
