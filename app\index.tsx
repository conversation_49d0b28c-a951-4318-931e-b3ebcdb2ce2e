import { ActivityIndicator, StyleSheet, Text, View } from 'react-native'
import React, { useEffect } from 'react'
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context'
import { router } from 'expo-router'

const App = () => {
    useEffect(() => {
        setTimeout(() => {
            router.replace('/(tabs)')
        }, 3000)
    }, [])
  return (
    <SafeAreaProvider>
        <SafeAreaView style={styles.container}>
            <ActivityIndicator size={'large'}/>
        </SafeAreaView>
    </SafeAreaProvider>
  )
}

export default App

const styles = StyleSheet.create({
    container:{
        flex:1,
        justifyContent:'center',
        alignItems:'center'
    }
})