import { useAuth } from '@/context/AuthContext'
import { useDatabase } from '@/context/DatabaseContext'
import { router } from 'expo-router'
import React, { useEffect } from 'react'
import { ActivityIndicator, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const App = () => {
    const { isAuthenticated, isLoading: authLoading } = useAuth()
    const { dbInitialized, dbError } = useDatabase()

    useEffect(() => {
        if (!authLoading && dbInitialized && !dbError) {
            if (isAuthenticated) {
                router.replace('/(tabs)')
            } else {
                router.replace('/login')
            }
        }
    }, [isAuthenticated, authLoading, dbInitialized, dbError])

    return (
        <SafeAreaView style={styles.container}>
            <ActivityIndicator size={'large'} color="#2563eb" />
        </SafeAreaView>
    )
}

export default App

const styles = StyleSheet.create({
    container:{
        flex:1,
        justifyContent:'center',
        alignItems:'center',
        backgroundColor: '#f8f9fa'
    }
})